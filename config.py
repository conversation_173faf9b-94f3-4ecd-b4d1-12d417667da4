"""
إعدادات البوت
"""

import os
from typing import Dict, List
from datetime import datetime, timedelta
import logging

# إعداد التسجيل
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# إعدادات البوت الأساسية
BOT_TOKEN = "7664465360:AAHPSTWu-NnROO2Wpfflv_gRCXKjrI6t724"  # ضع التوكن الخاص بك هنا

OPENAI_API_KEY = os.getenv('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', 
                           '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')

GEMINI_API_KEY = os.getenv('AIzaSyArMZdh0kS8x2wznchqq2S20aRYiO1RPhw', 
                           'AIzaSyA-i1f2UYV6QMc6a53ye5FUsMfcH9vaesE')

# معرفات الأدمن
ADMIN_IDS = [
    123456789,  # ضع معرف الأدمن الخاص بك هنا
]

# ملفات قاعدة البيانات
DATABASE_FILES = {
    'users': 'data/users.xlsx',
    'usage': 'data/usage.xlsx',
    'jobs': 'data/jobs.xlsx',
    'searches': 'data/searches.xlsx',
    'cv_analyses': 'data/cv_analyses.xlsx',
    'subscriptions': 'data/subscriptions.xlsx',
    'chat_sessions': 'data/chat_sessions.xlsx',
    'messages': 'data/messages.xlsx'
}

# رسائل البوت
MESSAGES = {
    'welcome': """
🎯 **مرحباً بك في بوت البحث عن الوظائف المتطور!**

🚀 **خدماتنا المميزة:**
• 🔍 **البحث الذكي:** ابحث في آلاف الوظائف من مصادر متعددة
• 📋 **تحليل السيرة الذاتية:** تحليل متقدم بالذكاء الاصطناعي
• 🎯 **المطابقة الذكية:** وظائف مناسبة لمهاراتك وخبرتك
• 💡 **الاستشارات المهنية:** نصائح مخصصة لتطوير مسيرتك

💎 **مميزات حصرية:**
• ✅ بحث متقدم بفلاتر ذكية
• ✅ تحليل شامل للسيرة الذاتية
• ✅ توصيات وظائف مخصصة
• ✅ دعم فني على مدار الساعة
• ✅ تحديثات مستمرة لقاعدة البيانات

🎁 **ابدأ رحلتك المهنية الآن - مجاناً!**

💡 **نصيحة:** ابدأ بتحليل سيرتك الذاتية للحصول على أفضل النتائج
""",
    
    'help': """
📚 **دليل استخدام البوت الشامل**

🔍 **البحث عن الوظائف:**
• 🎯 **بحث سريع:** ابحث في جميع الفئات بنقرة واحدة
• 🔧 **بحث متقدم:** حدد المجال والموقع بدقة
• ✍️ **بحث مخصص:** اكتب كلمات البحث المحددة
• 🎯 **وظائف مناسبة لي:** بناءً على تحليل سيرتك الذاتية

📋 **تحليل السيرة الذاتية:**
• 📎 **الصيغ المدعومة:** PDF, Word, Text
• 🤖 **تحليل ذكي:** استخراج المهارات والخبرات
• 💡 **توصيات مخصصة:** نصائح لتحسين سيرتك
• 🎯 **مطابقة الوظائف:** وظائف تناسب مهاراتك

💎 **خطط الاشتراك:**
• 🆓 **المجانية:** 5 بحثات + تحليل أساسي
• 💎 **الأساسية:** 50 بحث + ميزات متقدمة
• 👑 **المميزة:** بحث غير محدود + استشارات
• 🏢 **الشركات:** حلول مخصصة للفرق

📞 **الدعم والمساعدة:**
• 💬 **محادثة مباشرة:** مع فريق الدعم
• 📧 **بريد إلكتروني:** <EMAIL>
• 📱 **واتساب:** +20123456789
• ⏰ **متاح 24/7:** استجابة سريعة

🎯 **نصائح للاستخدام الأمثل:**
• ابدأ بتحليل سيرتك الذاتية
• استخدم كلمات مفتاحية دقيقة
• جرب البحث في فئات مختلفة
• تابع الوظائف الجديدة بانتظام
""",
    
    'subscription_expired': """
⏰ **انتهت صلاحية اشتراكك**

🔄 **لمتابعة الاستفادة من جميع الميزات:**
• قم بتجديد اشتراكك
• أو استخدم الباقة المجانية المحدودة

💎 **تجديد الاشتراك:**
• اختر الباقة المناسبة
• تواصل مع الدعم للتفعيل
""",
    
    'daily_limit_reached': """
📊 **تم الوصول للحد اليومي**

⏰ **الحد اليومي للباقة المجانية:**
• 5 بحثات يومياً
• 2 تحليل سيرة ذاتية

💎 **للحصول على المزيد:**
• ترقية للباقة المدفوعة
• انتظار حتى اليوم التالي
""",
    
    'error_general': """
❌ **حدث خطأ غير متوقع**

🔄 **يرجى المحاولة مرة أخرى**
📞 **إذا استمر الخطأ، تواصل مع الدعم**
""",
    
    'processing': """
⚙️ **جاري المعالجة...**

⏰ **يرجى الانتظار قليلاً**
🚀 **سنعرض النتائج قريباً**
""",
    
    'choose_category': """
🎯 **اختر فئة الوظيفة:**

💡 **نصيحة:** اختر الفئة الأقرب لتخصصك للحصول على أفضل النتائج
""",
    
    'choose_job_type': """
📋 **اختر نوع الوظيفة:**

💼 **أنواع الوظائف المتاحة:**
• دوام كامل - للباحثين عن استقرار وظيفي
• دوام جزئي - للمرونة في العمل
• عقد مؤقت - لمشاريع محددة
• عمل حر - للاستقلالية المهنية
• تدريب - لاكتساب الخبرة
• عن بعد - للعمل من أي مكان
""",
    
    'searching': """
🔍 **جاري البحث عن الوظائف...**

⏳ **يرجى الانتظار قليلاً**
🌐 **نبحث في أفضل مواقع التوظيف**
📊 **جمع النتائج وتصفيتها**

💡 **نصيحة:** كلما كانت معايير البحث أكثر دقة، كانت النتائج أفضل
""",
    
    'no_jobs': """
😔 **لم يتم العثور على وظائف مطابقة**

🔍 **اقتراحات للحصول على نتائج أفضل:**
• جرب توسيع نطاق البحث الجغرافي
• اختر فئة وظيفة أخرى مشابهة
• قلل من الفلاتر المطبقة
• جرب كلمات مفتاحية مختلفة

💡 **أو يمكنك:**
• البحث في فئات أخرى
• تعديل الفلاتر المتقدمة
• المحاولة مرة أخرى لاحقاً
"""
}

# فئات الوظائف
JOB_CATEGORIES = {
    'frontend': 'Front End Developer',
    'backend': 'Back End Developer',
    'fullstack': 'Full Stack Developer',
    'mobile': 'Mobile Developer',
    'devops': 'DevOps Engineer',
    'data_analysis': 'Data Analyst',
    'data_science': 'Data Scientist',
    'ai_ml': 'AI/ML Engineer',
    'ui_ux': 'UI/UX Designer',
    'marketing': 'Digital Marketing',
    'project_management': 'Project Manager',
    'hr': 'Human Resources',
    'sales': 'Sales Representative',
    'finance': 'Financial Analyst',
    'accounting': 'Accountant',
    'customer_service': 'Customer Service',
    'business_analyst': 'Business Analyst',
    'qa_tester': 'QA Tester',
    'cybersecurity': 'Cybersecurity Specialist',
    'network_admin': 'Network Administrator'
}

# المواقع والمدن
LOCATIONS = {
    'egypt': {
        'name': 'مصر',
        'cities': {
            'cairo': 'القاهرة',
            'giza': 'الجيزة',
            'alexandria': 'الإسكندرية',
            'sharm': 'شرم الشيخ',
            'hurghada': 'الغردقة',
            'luxor': 'الأقصر',
            'aswan': 'أسوان'
        }
    },
    'saudi': {
        'name': 'السعودية',
        'cities': {
            'riyadh': 'الرياض',
            'jeddah': 'جدة',
            'dammam': 'الدمام',
            'mecca': 'مكة المكرمة',
            'medina': 'المدينة المنورة',
            'khobar': 'الخبر',
            'tabuk': 'تبوك'
        }
    },
    'uae': {
        'name': 'الإمارات',
        'cities': {
            'dubai': 'دبي',
            'abu_dhabi': 'أبو ظبي',
            'sharjah': 'الشارقة',
            'ajman': 'عجمان',
            'fujairah': 'الفجيرة',
            'ras_al_khaimah': 'رأس الخيمة'
        }
    },
    'qatar': {
        'name': 'قطر',
        'cities': {
            'doha': 'الدوحة',
            'al_rayyan': 'الريان',
            'al_wakrah': 'الوكرة'
        }
    },
    'kuwait': {
        'name': 'الكويت',
        'cities': {
            'kuwait_city': 'مدينة الكويت',
            'hawalli': 'حولي',
            'farwaniya': 'الفروانية'
        }
    },
    'remote': {
        'name': 'عن بعد',
        'cities': {
            'remote': 'عن بعد'
        }
    }
}

# البلدان المدعومة (للتوافق مع الإصدارات السابقة)
SUPPORTED_COUNTRIES = {country: data['name'] for country, data in LOCATIONS.items()}

# أنواع الوظائف
JOB_TYPES = {
    'full_time': 'دوام كامل',
    'part_time': 'دوام جزئي',
    'contract': 'عقد مؤقت',
    'freelance': 'عمل حر',
    'internship': 'تدريب',
    'remote': 'عمل عن بُعد',
    'hybrid': 'عمل مختلط'
}

# إعدادات البحث
SEARCH_SETTINGS = {
    'max_results_free': 10,
    'max_results_basic': 50,
    'max_results_premium': 100,
    'max_results_enterprise': 500,
    'timeout_seconds': 30,
    'retry_attempts': 3,
    'delay_between_requests': 1
}

# إعدادات تحليل السيرة الذاتية
CV_ANALYSIS_SETTINGS = {
    'max_file_size_mb': 10,
    'supported_formats': ['.pdf', '.docx', '.doc', '.txt'],
    'max_text_length': 50000,
    'min_text_length': 100
}

# إعدادات الاشتراكات
SUBSCRIPTION_LIMITS = {
    'free': {
        'daily_searches': 5,
        'daily_cv_analyses': 2,
        'job_results_limit': 10,
        'features': ['basic_search', 'basic_cv_analysis']
    },
    'basic': {
        'daily_searches': 50,
        'daily_cv_analyses': 10,
        'job_results_limit': 50,
        'features': ['advanced_search', 'detailed_cv_analysis', 'job_alerts']
    },
    'premium': {
        'daily_searches': 200,
        'daily_cv_analyses': 50,
        'job_results_limit': 100,
        'features': ['premium_search', 'ai_cv_analysis', 'priority_support', 'custom_filters']
    },
    'enterprise': {
        'daily_searches': -1,  # غير محدود
        'daily_cv_analyses': -1,  # غير محدود
        'job_results_limit': 500,
        'features': ['all_features', 'api_access', 'dedicated_support', 'custom_integration']
    }
}

# حدود الاستخدام
USAGE_LIMITS = {
    'free': 5,
    'basic': 50,
    'pro': 100,
    'premium': -1,  # غير محدود
    'enterprise': -1  # غير محدود
}

# نطاقات الراتب للفلاتر المتقدمة
SALARY_RANGES = {
    'entry': {'min': 0, 'max': 3000, 'label': '$0 - $3,000'},
    'junior': {'min': 3000, 'max': 5000, 'label': '$3,000 - $5,000'},
    'mid': {'min': 5000, 'max': 8000, 'label': '$5,000 - $8,000'},
    'senior': {'min': 8000, 'max': 12000, 'label': '$8,000 - $12,000'},
    'lead': {'min': 12000, 'max': 20000, 'label': '$12,000 - $20,000'},
    'executive': {'min': 20000, 'max': 50000, 'label': '$20,000+'}
}

# مستويات الخبرة
EXPERIENCE_LEVELS = {
    'fresh': 'خريج جديد (0-1 سنة)',
    'junior': 'مبتدئ (1-3 سنوات)',
    'mid': 'متوسط (3-5 سنوات)',
    'senior': 'متقدم (5-8 سنوات)',
    'lead': 'خبير (8+ سنوات)'
}

# أنواع الشركات
COMPANY_TYPES = {
    'startup': 'شركة ناشئة',
    'small': 'شركة صغيرة',
    'medium': 'شركة متوسطة',
    'large': 'شركة كبيرة',
    'multinational': 'شركة متعددة الجنسيات',
    'government': 'قطاع حكومي',
    'nonprofit': 'منظمة غير ربحية'
}

# المهارات التقنية
TECHNICAL_SKILLS = {
    'javascript': 'JavaScript',
    'python': 'Python',
    'java': 'Java',
    'csharp': 'C#',
    'php': 'PHP',
    'react': 'React',
    'vue': 'Vue.js',
    'angular': 'Angular',
    'nodejs': 'Node.js',
    'django': 'Django',
    'flask': 'Flask',
    'spring': 'Spring',
    'laravel': 'Laravel',
    'mysql': 'MySQL',
    'postgresql': 'PostgreSQL',
    'mongodb': 'MongoDB',
    'redis': 'Redis',
    'aws': 'AWS',
    'azure': 'Microsoft Azure',
    'gcp': 'Google Cloud',
    'docker': 'Docker',
    'kubernetes': 'Kubernetes',
    'git': 'Git',
    'html': 'HTML',
    'css': 'CSS'
}

# إعدادات الشبكة
NETWORK_SETTINGS = {
    'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'request_timeout': 30,
    'max_retries': 3,
    'retry_delay': 2,
    'concurrent_requests': 5
}

# إعدادات التسجيل
LOGGING_SETTINGS = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file': 'bot.log',
    'max_file_size': 10 * 1024 * 1024,  # 10 MB
    'backup_count': 5
}

# مواقع البحث عن الوظائف
JOB_SITES = {
    'linkedin': {
        'name': 'LinkedIn',
        'base_url': 'https://www.linkedin.com/jobs/search',
        'enabled': True,
        'priority': 1
    },
    'indeed': {
        'name': 'Indeed',
        'base_url': 'https://www.indeed.com/jobs',
        'enabled': True,
        'priority': 2
    },
    'glassdoor': {
        'name': 'Glassdoor',
        'base_url': 'https://www.glassdoor.com/Job/jobs.htm',
        'enabled': False,  # معطل مؤقتاً
        'priority': 3
    },
    'monster': {
        'name': 'Monster',
        'base_url': 'https://www.monster.com/jobs/search',
        'enabled': False,
        'priority': 4
    }
}

# الكلمات المفتاحية للمهارات
SKILL_KEYWORDS = {
    'programming': [
        'python', 'java', 'javascript', 'c++', 'c#', 'php', 'ruby', 'go', 'rust',
        'html', 'css', 'react', 'angular', 'vue', 'node.js', 'django', 'flask',
        'spring', 'laravel', 'rails', 'express'
    ],
    'databases': [
        'mysql', 'postgresql', 'mongodb', 'oracle', 'sql server', 'redis',
        'elasticsearch', 'cassandra', 'dynamodb'
    ],
    'cloud': [
        'aws', 'azure', 'google cloud', 'docker', 'kubernetes', 'terraform',
        'jenkins', 'gitlab', 'github actions'
    ],
    'design': [
        'photoshop', 'illustrator', 'figma', 'sketch', 'indesign', 'after effects',
        'premiere pro', 'ui/ux', 'graphic design', 'web design'
    ],
    'business': [
        'project management', 'agile', 'scrum', 'kanban', 'lean', 'six sigma',
        'business analysis', 'data analysis', 'excel', 'powerpoint', 'tableau'
    ],
    'languages': [
        'arabic', 'english', 'french', 'german', 'spanish', 'italian',
        'chinese', 'japanese', 'korean', 'russian'
    ]
}

# رسائل الخطأ المخصصة
ERROR_MESSAGES = {
    'file_too_large': 'حجم الملف كبير جداً. الحد الأقصى المسموح هو {max_size} ميجابايت.',
    'unsupported_format': 'صيغة الملف غير مدعومة. الصيغ المدعومة: {formats}',
    'network_error': 'خطأ في الاتصال بالإنترنت. يرجى المحاولة مرة أخرى.',
    'rate_limit_exceeded': 'تم تجاوز الحد المسموح. يرجى الانتظار {wait_time} ثانية.',
    'invalid_input': 'البيانات المدخلة غير صحيحة. يرجى التحقق والمحاولة مرة أخرى.',
    'service_unavailable': 'الخدمة غير متاحة حالياً. يرجى المحاولة لاحقاً.',
    'permission_denied': 'ليس لديك صلاحية للوصول إلى هذه الميزة.',
    'subscription_required': 'هذه الميزة تتطلب اشتراك مدفوع.'
}

# إعدادات الأمان
SECURITY_SETTINGS = {
    'max_requests_per_minute': 30,
    'max_requests_per_hour': 500,
    'blocked_file_extensions': ['.exe', '.bat', '.cmd', '.scr', '.com'],
    'max_message_length': 4000,
    'spam_detection_enabled': True,
    'auto_ban_threshold': 10
}

# إعدادات الإشعارات
NOTIFICATION_SETTINGS = {
    'admin_notifications': True,
    'user_welcome_message': True,
    'subscription_reminders': True,
    'daily_reports': True,
    'error_alerts': True
}

# معلومات الاتصال
CONTACT_INFO = {
    'support_email': '<EMAIL>',
    'whatsapp': '+20123456789',
    'telegram': '@JobsBotSupport',
    'website': 'https://jobsbot.com',
    'working_hours': 'الأحد - الخميس: 9:00 ص - 6:00 م (GMT+2)'
}

# إعدادات التخزين المؤقت
CACHE_SETTINGS = {
    'job_results_ttl': 3600,  # ساعة واحدة
    'cv_analysis_ttl': 86400,  # 24 ساعة
    'user_session_ttl': 1800,  # 30 دقيقة
    'search_history_ttl': 604800  # أسبوع واحد
}

# إعدادات النسخ الاحتياطي
BACKUP_SETTINGS = {
    'enabled': True,
    'frequency': 'daily',
    'retention_days': 30,
    'backup_path': 'backups/',
    'compress': True
}

def get_database_path(db_type: str) -> str:
    """الحصول على مسار قاعدة البيانات"""
    return DATABASE_FILES.get(db_type, f'data/{db_type}.xlsx')

def get_subscription_limit(plan: str, limit_type: str) -> int:
    """الحصول على حد الاشتراك"""
    return SUBSCRIPTION_LIMITS.get(plan, {}).get(limit_type, 0)

def is_admin(user_id: int) -> bool:
    """التحقق من كون المستخدم أدمن"""
    return user_id in ADMIN_IDS

def get_supported_file_formats() -> List[str]:
    """الحصول على صيغ الملفات المدعومة"""
    return CV_ANALYSIS_SETTINGS['supported_formats']

def get_job_category_name(category_key: str) -> str:
    """الحصول على اسم فئة الوظيفة"""
    return JOB_CATEGORIES.get(category_key, 'غير محدد')

def get_country_name(country_key: str) -> str:
    """الحصول على اسم البلد"""
    return SUPPORTED_COUNTRIES.get(country_key, 'غير محدد')

def validate_file_size(file_size: int) -> bool:
    """التحقق من حجم الملف"""
    max_size = CV_ANALYSIS_SETTINGS['max_file_size_mb'] * 1024 * 1024
    return file_size <= max_size

def validate_file_format(filename: str) -> bool:
    """التحقق من صيغة الملف"""
    import os
    file_ext = os.path.splitext(filename)[1].lower()
    return file_ext in CV_ANALYSIS_SETTINGS['supported_formats']