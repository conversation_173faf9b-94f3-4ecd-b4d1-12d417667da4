# 🔧 دليل الإعداد والتكوين

## 📋 المتطلبات الأساسية

### 1. إنشاء بوت تليجرام
1. تحدث مع [@BotFather](https://t.me/BotFather) في تليجرام
2. أرسل `/newbot`
3. اختر اسماً للبوت
4. اختر معرفاً للبوت (يجب أن ينتهي بـ bot)
5. احفظ الـ Token الذي سيرسله لك

### 2. الحصول على مفاتيح الذكاء الاصطناعي (اختياري)

#### OpenAI API Key
1. اذهب إلى [OpenAI Platform](https://platform.openai.com/)
2. سجل حساباً جديداً أو سجل دخول
3. اذهب إلى API Keys
4. أنشئ مفتاح جديد واحفظه

#### Google Gemini API Key
1. اذهب إلى [Google AI Studio](https://makersuite.google.com/)
2. سجل دخول بحساب Google
3. أنشئ مفتاح API جديد
4. احفظ المفتاح

## ⚙️ تكوين البوت

### 1. تحديث ملف config.py

```python
# إعدادات البوت الأساسية
BOT_TOKEN = "1234567890:ABCdefGHIjklMNOpqrsTUVwxyz"  # ضع التوكن هنا

# معرفات الأدمن (ضع معرفك هنا)
ADMIN_IDS = [
    123456789,  # معرفك في تليجرام
    987654321,  # معرف أدمن آخر (اختياري)
]

# مفاتيح الذكاء الاصطناعي (اختياري)
OPENAI_API_KEY = "sk-..."  # مفتاح OpenAI
GEMINI_API_KEY = "AIza..."  # مفتاح Gemini
```

### 2. معرفة معرف تليجرام الخاص بك

#### الطريقة الأولى: استخدام بوت
1. تحدث مع [@userinfobot](https://t.me/userinfobot)
2. أرسل `/start`
3. سيرسل لك معرفك

#### الطريقة الثانية: استخدام بوت آخر
1. تحدث مع [@myidbot](https://t.me/myidbot)
2. أرسل `/getid`

## 🚀 تشغيل البوت

### الطريقة السهلة
```bash
python start_bot.py
```

### الطريقة اليدوية
```bash
# تثبيت المتطلبات
pip install -r requirements.txt

# تشغيل البوت
python main.py
```

## 🔧 إعدادات متقدمة

### تخصيص الرسائل
يمكنك تعديل الرسائل في ملف `config.py` في قسم `MESSAGES`:

```python
MESSAGES = {
    'welcome': "رسالة الترحيب المخصصة...",
    'help': "رسالة المساعدة المخصصة...",
    # ... باقي الرسائل
}
```

### تخصيص حدود الاستخدام
```python
SUBSCRIPTION_LIMITS = {
    'free': {
        'daily_searches': 10,  # زيادة عدد البحثات المجانية
        'daily_cv_analyses': 5,  # زيادة عدد تحليلات CV
        # ...
    }
}
```

### إضافة مواقع بحث جديدة
```python
JOB_SITES = {
    'new_site': {
        'name': 'موقع جديد',
        'base_url': 'https://example.com/jobs',
        'enabled': True,
        'priority': 5
    }
}
```

## 🗄️ إعداد قاعدة البيانات

البوت يستخدم ملفات Excel افتراضياً. الملفات ستُنشأ تلقائياً في مجلد `data/`:

- `users.xlsx` - بيانات المستخدمين
- `usage.xlsx` - سجل الاستخدام
- `jobs.xlsx` - الوظائف المحفوظة
- `cv_analyses.xlsx` - تحليلات السيرة الذاتية
- `subscriptions.xlsx` - بيانات الاشتراكات

### تغيير مسارات الملفات
```python
DATABASE_FILES = {
    'users': 'custom_path/users.xlsx',
    'usage': 'custom_path/usage.xlsx',
    # ...
}
```

## 🔒 إعدادات الأمان

### حماية من الاستخدام المفرط
```python
SECURITY_SETTINGS = {
    'max_requests_per_minute': 20,  # تقليل الحد الأقصى
    'max_requests_per_hour': 300,
    'auto_ban_threshold': 5,  # حظر تلقائي بعد 5 مخالفات
}
```

### تشفير البيانات الحساسة
```python
# في ملف منفصل .env
BOT_TOKEN=your_token_here
OPENAI_API_KEY=your_key_here
GEMINI_API_KEY=your_key_here
```

## 📊 إعداد المراقبة

### تفعيل التقارير اليومية
```python
NOTIFICATION_SETTINGS = {
    'daily_reports': True,
    'admin_notifications': True,
    'error_alerts': True,
}
```

### إعداد ملفات السجل
```python
LOGGING_SETTINGS = {
    'level': 'INFO',  # أو 'DEBUG' للمزيد من التفاصيل
    'file': 'logs/bot.log',
    'max_file_size': 10 * 1024 * 1024,  # 10 MB
}
```

## 🌐 إعداد الخادم

### تشغيل على خادم Linux
```bash
# إنشاء خدمة systemd
sudo nano /etc/systemd/system/jobsbot.service
```

```ini
[Unit]
Description=Jobs Bot
After=network.target

[Service]
Type=simple
User=your_user
WorkingDirectory=/path/to/bot
ExecStart=/usr/bin/python3 /path/to/bot/main.py
Restart=always

[Install]
WantedBy=multi-user.target
```

```bash
# تفعيل الخدمة
sudo systemctl enable jobsbot
sudo systemctl start jobsbot
```

### استخدام Docker
```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
CMD ["python", "main.py"]
```

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### خطأ في Token
```
telegram.error.InvalidToken: Invalid token
```
**الحل:** تأكد من صحة BOT_TOKEN في config.py

#### خطأ في الاتصال
```
aiohttp.client_exceptions.ClientConnectorError
```
**الحل:** تحقق من اتصال الإنترنت وإعدادات الجدار الناري

#### خطأ في قاعدة البيانات
```
FileNotFoundError: [Errno 2] No such file or directory: 'data/users.xlsx'
```
**الحل:** تأكد من وجود مجلد data أو شغل البوت مرة واحدة لإنشاء الملفات

#### خطأ في الذكاء الاصطناعي
```
openai.error.AuthenticationError
```
**الحل:** تحقق من صحة مفاتيح API

## 📞 الحصول على المساعدة

إذا واجهت مشاكل:

1. تحقق من ملفات السجل في `logs/`
2. شغل البوت في وضع DEBUG
3. راجع الأخطاء في Terminal
4. تواصل مع فريق الدعم

## 🔄 التحديثات

لتحديث البوت:

```bash
# نسخ احتياطي للإعدادات
cp config.py config.py.backup

# تحديث الكود
git pull origin main

# تثبيت المتطلبات الجديدة
pip install -r requirements.txt

# استعادة الإعدادات
cp config.py.backup config.py
```
