#!/usr/bin/env python3
"""
إعداد وتثبيت متطلبات البوت
"""

import subprocess
import sys
import os

def install_requirements():
    """تثبيت المتطلبات من ملف requirements.txt"""
    try:
        print("📦 جاري تثبيت المتطلبات...")
        
        # تحديث pip أولاً
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        
        # تثبيت المتطلبات الأساسية
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        
        print("✅ تم تثبيت جميع المتطلبات بنجاح")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت المتطلبات: {e}")
        return False
    except FileNotFoundError:
        print("❌ ملف requirements.txt غير موجود")
        return False

def install_optional_packages():
    """تثبيت الحزم الاختيارية للتحليل المتقدم"""
    optional_packages = [
        "spacy",
        "textstat",
        "nltk"
    ]
    
    print("\n🔧 جاري تثبيت الحزم الاختيارية...")
    
    for package in optional_packages:
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✅ تم تثبيت {package}")
        except subprocess.CalledProcessError:
            print(f"⚠️ فشل في تثبيت {package} (اختياري)")

def download_spacy_model():
    """تحميل نموذج spaCy للغة الإنجليزية"""
    try:
        print("\n🧠 جاري تحميل نموذج spaCy...")
        subprocess.check_call([sys.executable, "-m", "spacy", "download", "en_core_web_sm"])
        print("✅ تم تحميل نموذج spaCy")
        return True
    except subprocess.CalledProcessError:
        print("⚠️ فشل في تحميل نموذج spaCy (اختياري)")
        return False

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    directories = ["data", "backups", "logs"]
    
    print("\n📁 جاري إنشاء المجلدات...")
    
    for directory in directories:
        try:
            os.makedirs(directory, exist_ok=True)
            print(f"✅ تم إنشاء مجلد {directory}")
        except Exception as e:
            print(f"❌ فشل في إنشاء مجلد {directory}: {e}")

def setup_environment():
    """إعداد البيئة"""
    print("🔧 جاري إعداد البيئة...")
    
    # إنشاء ملف .env إذا لم يكن موجوداً
    if not os.path.exists('.env'):
        env_content = """# إعدادات البوت
BOT_TOKEN=your_bot_token_here
OPENAI_API_KEY=your_openai_key_here
GEMINI_API_KEY=your_gemini_key_here

# إعدادات قاعدة البيانات
DATABASE_URL=sqlite:///bot.db

# إعدادات أخرى
DEBUG=False
LOG_LEVEL=INFO
"""
        try:
            with open('.env', 'w', encoding='utf-8') as f:
                f.write(env_content)
            print("✅ تم إنشاء ملف .env")
        except Exception as e:
            print(f"❌ فشل في إنشاء ملف .env: {e}")

def main():
    """الدالة الرئيسية للإعداد"""
    print("🚀 بدء إعداد بوت البحث عن الوظائف")
    print("=" * 50)
    
    # إنشاء المجلدات
    create_directories()
    
    # إعداد البيئة
    setup_environment()
    
    # تثبيت المتطلبات
    if not install_requirements():
        print("❌ فشل في تثبيت المتطلبات الأساسية")
        return False
    
    # تثبيت الحزم الاختيارية
    install_optional_packages()
    
    # تحميل نموذج spaCy
    download_spacy_model()
    
    print("\n" + "=" * 50)
    print("🎉 تم إعداد البوت بنجاح!")
    print("\n📋 الخطوات التالية:")
    print("1. قم بتحديث ملف config.py بمعلومات البوت الخاص بك")
    print("2. أضف BOT_TOKEN في ملف config.py")
    print("3. أضف مفاتيح API للذكاء الاصطناعي (اختياري)")
    print("4. شغل الاختبار: python test_bot.py")
    print("5. شغل البوت: python main.py")
    
    return True

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الإعداد")
    except Exception as e:
        print(f"\n❌ خطأ في الإعداد: {e}")
