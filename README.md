# 🤖 بوت البحث عن الوظائف الذكي

بوت تليجرام متطور للبحث عن الوظائف وتحليل السيرة الذاتية باستخدام الذكاء الاصطناعي.

## ✨ المميزات

### 🔍 البحث عن الوظائف
- بحث ذكي في مواقع التوظيف المختلفة
- فلاتر متقدمة (الراتب، الموقع، نوع الوظيفة)
- نتائج مخصصة حسب المهارات والخبرة
- تصدير النتائج إلى Excel

### 📄 تحليل السيرة الذاتية
- تحليل متقدم باستخدام الذكاء الاصطناعي
- استخراج المهارات والخبرات تلقائياً
- تقييم جودة السيرة الذاتية
- مطابقة السيرة مع الوظائف المناسبة
- توصيات للتحسين

### 💎 نظام الاشتراكات
- باقة مجانية مع ميزات أساسية
- باقات مدفوعة مع ميزات متقدمة
- إدارة شاملة للاشتراكات
- طرق دفع متعددة

### 💬 الدعم الفني
- محادثة مباشرة مع فريق الدعم
- نظام تذاكر متطور
- استجابة سريعة للاستفسارات

## 🚀 التثبيت والإعداد

### المتطلبات
- Python 3.8 أو أحدث
- حساب بوت في تليجرام
- مفاتيح API للذكاء الاصطناعي (اختياري)

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone <repository-url>
cd jobs-bot
```

2. **تشغيل الإعداد التلقائي**
```bash
python setup.py
```

3. **تحديث الإعدادات**
   - افتح ملف `config.py`
   - أضف `BOT_TOKEN` الخاص بك
   - أضف مفاتيح API للذكاء الاصطناعي (اختياري)

4. **اختبار البوت**
```bash
python test_bot.py
```

5. **تشغيل البوت**
```bash
python main.py
```

## ⚙️ الإعدادات

### إعدادات البوت الأساسية
```python
# في ملف config.py
BOT_TOKEN = "your_bot_token_here"
ADMIN_IDS = [123456789]  # معرفات الأدمن
```

### إعدادات الذكاء الاصطناعي
```python
OPENAI_API_KEY = "your_openai_key"
GEMINI_API_KEY = "your_gemini_key"
```

## 📁 هيكل المشروع

```
jobs-bot/
├── main.py              # الملف الرئيسي للبوت
├── config.py            # إعدادات البوت
├── requirements.txt     # متطلبات Python
├── setup.py            # سكريبت الإعداد
├── test_bot.py         # اختبار البوت
├── README.md           # هذا الملف
├── data/               # ملفات قاعدة البيانات
│   ├── users.xlsx
│   ├── usage.xlsx
│   └── ...
└── backups/            # النسخ الاحتياطية
```

## 🔧 الاستخدام

### للمستخدمين
1. ابدأ محادثة مع البوت: `/start`
2. اختر الخدمة المطلوبة من القائمة
3. اتبع التعليمات التفاعلية

### للأدمن
- `/admin` - لوحة تحكم الأدمن
- `/stats` - عرض الإحصائيات
- `/users` - قائمة المستخدمين
- `/broadcast` - رسالة جماعية

## 🛠️ التطوير

### إضافة ميزات جديدة
1. أضف الدوال المطلوبة في `main.py`
2. حدث معالج الأزرار `button_handler`
3. أضف الرسائل في `config.py`
4. اختبر الميزة الجديدة

### قاعدة البيانات
البوت يستخدم ملفات Excel لتخزين البيانات:
- `users.xlsx` - بيانات المستخدمين
- `usage.xlsx` - سجل الاستخدام
- `jobs.xlsx` - الوظائف المحفوظة
- `cv_analyses.xlsx` - تحليلات السيرة الذاتية

## 🔒 الأمان

- جميع البيانات الحساسة محمية
- تشفير مفاتيح API
- تسجيل شامل للأنشطة
- حماية من الاستخدام المفرط

## 📊 المراقبة

البوت يتضمن نظام مراقبة شامل:
- إحصائيات الاستخدام
- مراقبة أداء API
- تنبيهات الأخطاء
- تقارير دورية

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إضافة الاختبارات المناسبة
4. إرسال Pull Request

## 📞 الدعم

- **البريد الإلكتروني:** <EMAIL>
- **تليجرام:** @JobsBotSupport
- **الموقع:** https://jobsbot.com

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف LICENSE للتفاصيل.

## 🙏 شكر خاص

- فريق python-telegram-bot
- مجتمع الذكاء الاصطناعي مفتوح المصدر
- جميع المساهمين في المشروع

---

**ملاحظة:** هذا البوت في مرحلة التطوير المستمر. نرحب بالاقتراحات والتحسينات!
