#!/usr/bin/env python3
"""
سكريبت بدء تشغيل البوت مع فحص المتطلبات
"""

import sys
import os
import subprocess
import importlib.util

def check_python_version():
    """التحقق من إصدار Python"""
    if sys.version_info < (3, 8):
        print("❌ يتطلب Python 3.8 أو أحدث")
        print(f"الإصدار الحالي: {sys.version}")
        return False
    print(f"✅ إصدار Python مناسب: {sys.version_info.major}.{sys.version_info.minor}")
    return True

def check_required_packages():
    """التحقق من وجود الحزم المطلوبة"""
    required_packages = [
        'telegram',
        'pandas',
        'aiohttp',
        'beautifulsoup4',
        'python-docx',
        'PyPDF2',
        'openai',
        'google.generativeai'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'python-docx':
                importlib.import_module('docx')
            elif package == 'beautifulsoup4':
                importlib.import_module('bs4')
            elif package == 'google.generativeai':
                importlib.import_module('google.generativeai')
            else:
                importlib.import_module(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} غير مثبت")
            missing_packages.append(package)
    
    return missing_packages

def install_missing_packages(packages):
    """تثبيت الحزم المفقودة"""
    if not packages:
        return True
    
    print(f"\n📦 جاري تثبيت {len(packages)} حزمة مفقودة...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install"
        ] + packages)
        print("✅ تم تثبيت جميع الحزم المفقودة")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت الحزم: {e}")
        return False

def check_config():
    """التحقق من ملف الإعدادات"""
    try:
        from config import BOT_TOKEN, ADMIN_IDS
        
        if not BOT_TOKEN or BOT_TOKEN == "your_bot_token_here":
            print("⚠️ يرجى تحديث BOT_TOKEN في ملف config.py")
            return False
        
        if not ADMIN_IDS or ADMIN_IDS == [123456789]:
            print("⚠️ يرجى تحديث ADMIN_IDS في ملف config.py")
        
        print("✅ ملف الإعدادات صحيح")
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد الإعدادات: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ في فحص الإعدادات: {e}")
        return False

def check_data_directory():
    """التحقق من مجلد البيانات"""
    if not os.path.exists('data'):
        print("📁 إنشاء مجلد البيانات...")
        os.makedirs('data', exist_ok=True)
    print("✅ مجلد البيانات موجود")
    return True

def run_bot():
    """تشغيل البوت"""
    try:
        print("🚀 بدء تشغيل البوت...")
        from main import TelegramJobBot
        import asyncio
        
        bot = TelegramJobBot()
        asyncio.run(bot.run())
        
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل البوت: {e}")
        return False
    
    return True

def main():
    """الدالة الرئيسية"""
    print("🤖 بوت البحث عن الوظائف الذكي")
    print("=" * 40)
    
    # فحص إصدار Python
    if not check_python_version():
        return False
    
    # فحص الحزم المطلوبة
    print("\n📦 فحص الحزم المطلوبة...")
    missing = check_required_packages()
    
    if missing:
        print(f"\n⚠️ يوجد {len(missing)} حزمة مفقودة")
        install = input("هل تريد تثبيتها تلقائياً؟ (y/n): ").lower().strip()
        
        if install in ['y', 'yes', 'نعم']:
            if not install_missing_packages(missing):
                print("❌ فشل في تثبيت الحزم. يرجى تثبيتها يدوياً:")
                print(f"pip install {' '.join(missing)}")
                return False
        else:
            print("❌ لا يمكن تشغيل البوت بدون الحزم المطلوبة")
            return False
    
    # فحص مجلد البيانات
    print("\n📁 فحص مجلد البيانات...")
    check_data_directory()
    
    # فحص الإعدادات
    print("\n⚙️ فحص الإعدادات...")
    if not check_config():
        print("\n📝 يرجى تحديث ملف config.py بالمعلومات الصحيحة")
        return False
    
    print("\n" + "=" * 40)
    print("✅ جميع الفحوصات نجحت!")
    print("🚀 بدء تشغيل البوت...")
    print("=" * 40)
    
    # تشغيل البوت
    return run_bot()

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n❌ فشل في تشغيل البوت")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف البرنامج")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
