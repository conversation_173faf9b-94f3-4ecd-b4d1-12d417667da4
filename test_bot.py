#!/usr/bin/env python3
"""
اختبار بسيط للبوت للتأكد من عمله
"""

import asyncio
import logging
from main import TelegramJobBot

# إعداد التسجيل
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

async def test_bot_initialization():
    """اختبار تهيئة البوت"""
    try:
        print("🔄 جاري اختبار تهيئة البوت...")
        
        # إنشاء البوت
        bot = TelegramJobBot()
        
        # اختبار تهيئة قاعدة البيانات
        print("✅ تم تهيئة قاعدة البيانات بنجاح")
        
        # اختبار تهيئة الذكاء الاصطناعي
        if bot.available_apis:
            print(f"✅ تم تهيئة الذكاء الاصطناعي: {', '.join(bot.available_apis)}")
        else:
            print("⚠️ لا توجد APIs متاحة للذكاء الاصطناعي")
        
        # اختبار تهيئة المدراء
        if hasattr(bot, 'subscription_manager'):
            print("✅ تم تهيئة مدير الاشتراكات")
        
        if hasattr(bot, 'chat_manager'):
            print("✅ تم تهيئة مدير المحادثة")
        
        if hasattr(bot, 'session_manager'):
            print("✅ تم تهيئة مدير الجلسات")
        
        # اختبار محلل السيرة الذاتية
        if hasattr(bot, 'cv_analyzer'):
            print("✅ تم تهيئة محلل السيرة الذاتية")
        
        print("🎉 تم اختبار البوت بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ فشل في اختبار البوت: {e}")
        logger.error(f"خطأ في الاختبار: {e}")
        return False

async def test_ai_response():
    """اختبار استجابة الذكاء الاصطناعي"""
    try:
        print("\n🤖 جاري اختبار الذكاء الاصطناعي...")
        
        bot = TelegramJobBot()
        
        if not bot.available_apis:
            print("⚠️ لا توجد APIs متاحة للاختبار")
            return False
        
        # اختبار بسيط
        test_prompt = "مرحبا، هذا اختبار بسيط. أجب بكلمة واحدة: نجح"
        response = await bot.generate_ai_response(test_prompt)
        
        if response and "نجح" in response.lower():
            print("✅ اختبار الذكاء الاصطناعي نجح")
            return True
        else:
            print(f"⚠️ استجابة غير متوقعة: {response[:100]}...")
            return True  # لا نعتبرها فشل كامل
        
    except Exception as e:
        print(f"❌ فشل اختبار الذكاء الاصطناعي: {e}")
        return False

async def test_database_operations():
    """اختبار عمليات قاعدة البيانات"""
    try:
        print("\n🗄️ جاري اختبار قاعدة البيانات...")
        
        bot = TelegramJobBot()
        
        # اختبار تسجيل مستخدم
        bot.register_user(12345, "Test User", "testuser")
        print("✅ تم اختبار تسجيل المستخدم")
        
        # اختبار تسجيل النشاط
        bot.log_user_action(12345, "test_action", {"test": "data"})
        print("✅ تم اختبار تسجيل النشاط")
        
        print("✅ اختبار قاعدة البيانات نجح")
        return True
        
    except Exception as e:
        print(f"❌ فشل اختبار قاعدة البيانات: {e}")
        return False

async def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار البوت الشامل")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 3
    
    # اختبار التهيئة
    if await test_bot_initialization():
        tests_passed += 1
    
    # اختبار قاعدة البيانات
    if await test_database_operations():
        tests_passed += 1
    
    # اختبار الذكاء الاصطناعي
    if await test_ai_response():
        tests_passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 نتائج الاختبار: {tests_passed}/{total_tests} نجح")
    
    if tests_passed == total_tests:
        print("🎉 جميع الاختبارات نجحت! البوت جاهز للتشغيل")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت، لكن البوت قد يعمل بشكل جزئي")
        return False

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        if result:
            print("\n✅ يمكنك الآن تشغيل البوت باستخدام: python main.py")
        else:
            print("\n⚠️ تحقق من الأخطاء قبل تشغيل البوت")
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الاختبار")
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
